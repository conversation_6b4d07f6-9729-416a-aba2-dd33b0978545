# 最终完整训练命令 - 所有参数与官方权重完全对齐

source ~/miniconda3/etc/profile.d/conda.sh && conda activate idp3 && \
python train.py --config-name=idp3_official_aligned \
    task.dataset.zarr_path=/home/<USER>/code/company/torqueidp3/data/raw_pour_converted \
    task.dataset.seed=42 \
    task.dataset.val_ratio=0.0 \
    task.dataset.max_train_episodes=90 \
    \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=1000 \
    training.lr_scheduler=cosine \
    training.lr_warmup_steps=200 \
    training.gradient_accumulate_every=1 \
    training.use_ema=True \
    training.resume=True \
    training.debug=False \
    training.save_video=True \
    training.tqdm_interval_sec=1.0 \
    \
    optimizer.lr=1.0e-4 \
    optimizer.weight_decay=1.0e-6 \
    optimizer.betas=[0.95,0.999] \
    optimizer.eps=1.0e-8 \
    \
    dataloader.batch_size=32 \
    dataloader.num_workers=8 \
    dataloader.shuffle=True \
    dataloader.pin_memory=True \
    dataloader.persistent_workers=False \
    \
    policy.noise_scheduler.num_train_timesteps=50 \
    policy.noise_scheduler.beta_start=0.0001 \
    policy.noise_scheduler.beta_end=0.02 \
    policy.noise_scheduler.beta_schedule=squaredcos_cap_v2 \
    policy.noise_scheduler.clip_sample=True \
    policy.noise_scheduler.set_alpha_to_one=True \
    policy.noise_scheduler.steps_offset=0 \
    policy.noise_scheduler.prediction_type=sample \
    policy.num_inference_steps=10 \
    \
    policy.diffusion_step_embed_dim=128 \
    policy.down_dims=[256,512,1024] \
    policy.crop_shape=[80,80] \
    policy.kernel_size=5 \
    policy.n_groups=8 \
    policy.use_point_crop=True \
    policy.use_down_condition=True \
    policy.use_mid_condition=True \
    policy.use_up_condition=True \
    policy.pointnet_type=multi_stage_pointnet \
    policy.point_downsample=True \
    policy.use_pc_color=False \
    \
    policy.se3_augmentation_cfg.use_aug=False \
    policy.se3_augmentation_cfg.rotation=False \
    policy.se3_augmentation_cfg.translation=True \
    policy.se3_augmentation_cfg.jitter=True \
    \
    policy.pointcloud_encoder_cfg.in_channels=3 \
    policy.pointcloud_encoder_cfg.out_channels=128 \
    policy.pointcloud_encoder_cfg.use_layernorm=True \
    policy.pointcloud_encoder_cfg.final_norm=layernorm \
    policy.pointcloud_encoder_cfg.normal_channel=False \
    policy.pointcloud_encoder_cfg.num_points=4096 \
    \
    ema.power=0.75 \
    ema.max_value=0.9999 \
    ema.min_value=0.0 \
    ema.inv_gamma=1.0 \
    ema.update_after_step=0 \
    \
    checkpoint.save_ckpt=True \
    checkpoint.topk.monitor_key=test_mean_score \
    checkpoint.topk.mode=max \
    checkpoint.topk.k=0 \
    checkpoint.save_last_ckpt=True \
    checkpoint.save_last_snapshot=False \
    \
    training.checkpoint_every=100 \
    training.val_every=100 \
    training.sample_every=5 \
    training.rollout_every=400 \
    \
    exp_name=gr1_dex-3d-official-aligned-seed0 \
    logging.mode=offline \
    logging.project=gr1_dex-3d-official-aligned-seed0

# 关键配置确认：
# ✅ Workspace: TrainDiffusionUnetHybridPointcloudWorkspace
# ✅ 训练种子: 0 (与官方一致)
# ✅ 数据种子: 42 (与官方一致)  
# ✅ 验证集比例: 0.0 (与官方一致)
# ✅ 最大训练episodes: 90 (与官方一致)
# ✅ 所有网络架构参数: 与官方完全一致
# ✅ 所有训练参数: 与官方完全一致
# ✅ SE3增强配置: 与官方完全一致
# ✅ EMA配置: 与官方完全一致
# ✅ 检查点配置: 与官方完全一致
