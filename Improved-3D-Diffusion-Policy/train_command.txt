# 完整训练命令 - 一行版本
python train.py --config-name=idp3_official_aligned task.dataset.zarr_path=data/raw_pour_converted training.seed=0 training.device=cuda:0 training.num_epochs=301 optimizer.lr=1.0e-4 dataloader.batch_size=64 exp_name=gr1_dex-3d-official-aligned-seed0 logging.mode=online

# 如果上面的命令太长，可以分步执行：

# 第一步：基础训练命令
python train.py --config-name=idp3_official_aligned \
    task.dataset.zarr_path=data/raw_pour_converted \
    training.seed=0 \
    training.device=cuda:0 \
    exp_name=gr1_dex-3d-official-aligned-seed0

# 第二步：如果需要调整更多参数
python train.py --config-name=idp3_official_aligned \
    task.dataset.zarr_path=data/raw_pour_converted \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=301 \
    optimizer.lr=1.0e-4 \
    optimizer.weight_decay=1.0e-6 \
    dataloader.batch_size=64 \
    policy.noise_scheduler.num_train_timesteps=50 \
    policy.num_inference_steps=10 \
    ema.power=0.75 \
    exp_name=gr1_dex-3d-official-aligned-seed0 \
    logging.mode=online
