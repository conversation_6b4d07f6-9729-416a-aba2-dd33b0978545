from typing import Dict
import torch
import numpy as np
import copy
from diffusion_policy_3d.common.pytorch_util import dict_apply
from diffusion_policy_3d.common.replay_buffer import ReplayBuffer
from diffusion_policy_3d.common.sampler import (SequenceSampler, get_val_mask, downsample_mask)
from diffusion_policy_3d.model.common.normalizer import LinearNormalizer, SingleFieldLinearNormalizer
from diffusion_policy_3d.dataset.base_dataset import BaseDataset
import diffusion_policy_3d.model.vision_3d.point_process as point_process
from termcolor import cprint

class GR1DexDatasetMultiCam(BaseDataset):
    def __init__(self,
            zarr_path, 
            horizon=1,
            pad_before=0,
            pad_after=0,
            seed=42,
            val_ratio=0.0,
            max_train_episodes=None,
            use_language=False,
            use_img=False,
            use_front_cam=True,
            num_points_front=4096,
            use_right_cam=False,
            num_points_right=1024,
            ):
        super().__init__()
        cprint(f'Loading GR1DexDatasetMultiCam from {zarr_path}', 'green')
        
        self.use_language = use_language
        self.use_img = use_img
        self.use_front_cam = use_front_cam
        self.num_points_front = num_points_front
        self.use_right_cam = use_right_cam
        self.num_points_right = num_points_right

        buffer_keys = [
            'state', 
            'action',
        ]
        
        # Add point cloud keys based on camera configuration
        if self.use_front_cam:
            buffer_keys.append('point_cloud')  # Assume this is front camera data
        if self.use_right_cam:
            # For now, we'll use the same point cloud data for both cameras
            # In a real multi-cam setup, you'd have separate keys
            pass
            
        self.replay_buffer = ReplayBuffer.copy_from_path(
            zarr_path, keys=buffer_keys)
        
        val_mask = get_val_mask(
            n_episodes=self.replay_buffer.n_episodes, 
            val_ratio=val_ratio,
            seed=seed)
        train_mask = ~val_mask
        train_mask = downsample_mask(
            mask=train_mask, 
            max_n=max_train_episodes, 
            seed=seed)
        self.sampler = SequenceSampler(
            replay_buffer=self.replay_buffer, 
            sequence_length=horizon,
            pad_before=pad_before, 
            pad_after=pad_after,
            episode_mask=train_mask)
        self.train_mask = train_mask
        self.val_mask = val_mask
        self.horizon = horizon
        self.pad_before = pad_before
        self.pad_after = pad_after

    def get_validation_dataset(self):
        val_set = copy.copy(self)
        val_set.sampler = SequenceSampler(
            replay_buffer=self.replay_buffer, 
            sequence_length=self.horizon,
            pad_before=self.pad_before, 
            pad_after=self.pad_after,
            episode_mask=self.val_mask
            )
        val_set.train_mask = self.val_mask
        return val_set

    def get_normalizer(self, mode='limits', **kwargs):
        data = {'action': self.replay_buffer['action']}
        normalizer = LinearNormalizer()
        normalizer.fit(data=data, last_n_dims=1, mode=mode, **kwargs)

        # Add normalizers for all observation types
        if self.use_front_cam:
            normalizer['front_point_cloud'] = SingleFieldLinearNormalizer.create_identity()
        if self.use_right_cam:
            normalizer['right_point_cloud'] = SingleFieldLinearNormalizer.create_identity()
        if self.use_img:
            normalizer['front_image'] = SingleFieldLinearNormalizer.create_identity()
            normalizer['right_image'] = SingleFieldLinearNormalizer.create_identity()
        
        normalizer['agent_pos'] = SingleFieldLinearNormalizer.create_identity()
        
        return normalizer

    def __len__(self) -> int:
        return len(self.sampler)

    def _sample_to_data(self, sample):
        agent_pos = sample['state'][:,].astype(np.float32)
        
        obs_dict = {
            'agent_pos': agent_pos,
        }
        
        # Process point clouds based on camera configuration
        if self.use_front_cam:
            front_point_cloud = sample['point_cloud'][:,].astype(np.float32)
            front_point_cloud = point_process.uniform_sampling_numpy(front_point_cloud, self.num_points_front)
            obs_dict['front_point_cloud'] = front_point_cloud
            
        if self.use_right_cam:
            # For now, use the same point cloud data but with different sampling
            # In a real setup, you'd have separate right camera data
            right_point_cloud = sample['point_cloud'][:,].astype(np.float32)
            right_point_cloud = point_process.uniform_sampling_numpy(right_point_cloud, self.num_points_right)
            obs_dict['right_point_cloud'] = right_point_cloud
            
        # Add dummy image data if required (since official config expects images)
        if self.use_img:
            # Create dummy images with the expected shape [T, 3, 84, 84]
            T = agent_pos.shape[0]
            obs_dict['front_image'] = np.zeros((T, 3, 84, 84), dtype=np.float32)
            obs_dict['right_image'] = np.zeros((T, 3, 84, 84), dtype=np.float32)
        
        data = {
            'obs': obs_dict,
            'action': sample['action'].astype(np.float32)
        }
           
        return data
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.sampler.sample_sequence(idx)
        data = self._sample_to_data(sample)
        to_torch_function = lambda x: torch.from_numpy(x) if x.__class__.__name__ == 'ndarray' else x
        torch_data = dict_apply(data, to_torch_function)
        return torch_data
