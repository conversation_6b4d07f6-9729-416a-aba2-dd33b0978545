#!/bin/bash

# 激进训练脚本 - 小batch size，更多epochs
# 适用于小数据集的深度训练

echo "=========================================="
echo "开始激进训练 - 小batch size + 更多epochs"
echo "=========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export WANDB_SILENT=True

# 激进训练命令
python train.py \
    --config-name=idp3_official_aligned \
    \
    `# 数据配置` \
    task.dataset.zarr_path=/home/<USER>/code/company/torqueidp3/data/raw_pour_converted \
    task.dataset.seed=42 \
    task.dataset.val_ratio=0.0 \
    task.dataset.max_train_episodes=90 \
    \
    `# 激进训练配置` \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=1000 \
    training.lr_scheduler=cosine \
    training.lr_warmup_steps=100 \
    training.gradient_accumulate_every=1 \
    training.use_ema=True \
    training.resume=True \
    \
    `# 小batch size配置` \
    dataloader.batch_size=16 \
    dataloader.num_workers=8 \
    dataloader.shuffle=True \
    dataloader.pin_memory=True \
    \
    `# 更频繁的检查点` \
    training.checkpoint_every=25 \
    training.val_every=25 \
    training.sample_every=5 \
    training.rollout_every=100 \
    \
    `# 其他关键参数保持与官方一致` \
    optimizer.lr=1.0e-4 \
    optimizer.weight_decay=1.0e-6 \
    policy.noise_scheduler.num_train_timesteps=50 \
    policy.num_inference_steps=10 \
    ema.power=0.75 \
    \
    exp_name=gr1_dex-3d-aggressive-bs16-ep1000 \
    logging.mode=offline \
    logging.project=gr1_dex-3d-aggressive

echo "=========================================="
echo "激进训练配置："
echo "  - Batch size: 16 (每epoch约140步)"
echo "  - Epochs: 1000"
echo "  - 总训练步数: ~140,000"
echo "  - 检查点频率: 每25 epochs"
echo "=========================================="
